<!-- 人员参会查询 -->
<template>
    <div class="judge-attendance-container">
        <div class="flex">
            <div class="query-container">
                <PeopleSelector
                    class="selector"
                    placeholder="人员"
                    v-model="accountList"
                    :options="externalOptions"
                ></PeopleSelector>
                <div class="flex">
                    <el-date-picker
                        class="selector date-selector"
                        v-model="week"
                        type="week"
                        placeholder="选择周"
                        ref="dataPicker"
                        :format="dateFormat"
                        :clearable="false"
                        :picker-options="pickerOptions"
                    >
                    </el-date-picker>
                    <el-button
                        class="qucik-access"
                        type="primary"
                        @click="handleQuickAccess('本周')"
                        >本周</el-button
                    >
                    <el-button type="primary" @click="handleQuickAccess('下周')"
                        >下周</el-button
                    >
                </div>
            </div>

            <el-button class="action-button" type="primary" @click="getList"
                >查询</el-button
            >
            <el-button class="action-button" type="primary" @click="handleReset"
                >重置</el-button
            >
        </div>
        <el-table
            class="judge-attendance-table"
            :data="tableList"
            :header-cell-style="{
                'text-align': 'center',
                'border': '1px solid #8c8c8c'
            }"
            :cell-style="{ border: '1px solid #8c8c8c!important' }"
            height="calc(100vh - 170px)"
            empty-text="无评委参会数据"
        >
            <el-table-column prop="userName" label="人员" align="center">
            </el-table-column>
            <el-table-column
                v-for="(i, index) in dateArr"
                :label="i"
                :key="i"
                align="center"
            >
                <el-table-column
                    :key="weekList[index]"
                    :label="weekList[index]"
                    align="center"
                >
                    <el-table-column
                        v-for="j in timePeriodList"
                        :key="j"
                        :label="j"
                        align="center"
                        width="70"
                    >
                        <template slot-scope="scope">
                            <el-popover
                                placement="top-start"
                                width="500"
                                trigger="hover"
                            >
                                <div slot="reference">
                                    {{
                                        handleWeekData(
                                            scope.row,
                                            dateArr[index],
                                            j,
                                            'numbers'
                                        ) || ''
                                    }}
                                </div>
                                <!-- <el-table
                                    style="width: 100%"
                                    :header-cell-style="{
                                        'text-align': 'center'
                                    }"
                                    :data="
                                        handleWeekData(
                                            scope.row,
                                            dateArr[index],
                                            j,
                                            'tableList'
                                        )
                                    "
                                >
                                    <el-table-column
                                        prop="startTime"
                                        label="预计开始时间"
                                        min-width="100"
                                        align="center"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="endTime"
                                        label="预计完成时间"
                                        min-width="100"
                                        align="center"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                        prop="meetingTitle"
                                        label="会议名称"
                                        min-width="200"
                                    >
                                    </el-table-column>
                                </el-table> -->
                            </el-popover>
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants.js';
import moment from 'moment';
import PeopleSelector from 'Components/PeopleSelector.vue';
import { getDaysOfWeek } from '../../commonFunction';

const weekList = [
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六',
    '星期日'
];
const timePeriodList = ['上午', '下午'];
export default {
    name: 'JudgeAttendanceQuery',
    components: { PeopleSelector },
    data() {
        return {
            week: new Date(),
            pickerOptions: {
                firstDayOfWeek: 1
            },
            CONSTANTS,
            tableList: [],
            dateArr: [],
            weekList,
            timePeriodList,
            accountList: []
        };
    },
    computed: {
        dateFormat() {
            const startOfWeek = moment(this.week)
                .startOf('week')
                .format('YYYY/M/D');
            const endOfWeek = moment(this.week)
                .endOf('week')
                .format('YYYY/M/D');
            return `${startOfWeek} - ${endOfWeek}`;
        },
        externalOptions() {
            return this.$store.state.feature.externalStaff;
        }
    },
    mounted() {
        this.dateArr = getDaysOfWeek(this.week, 'YYYY-MM-DD');
        this.getList();
    },
    activated() {
        this.dateArr = getDaysOfWeek(this.week, 'YYYY-MM-DD');
        this.getList();
    },
    methods: {
        /**
         * 重置
         */
        handleReset() {
            this.accountList = [];
            this.handleQuickAccess('本周');
            this.getList();
        },
        /**
         * 获取列表
         */
        async getList() {
            const api = this.$service.feature.meeting.getJudgeAttendanceInfo;
            const startDate = moment(this.week)
                .startOf('week')
                .format('YYYY-MM-DD');
            const endDate = moment(this.week)
                .endOf('week')
                .format('YYYY-MM-DD');
            const params = {
                currentPage: this.page,
                endDate,
                accountList: this.accountList,
                pageSize: this.limit,
                startDate
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.dateArr = getDaysOfWeek(this.week, 'YYYY-MM-DD');
                    this.tableList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 点击快捷按钮的处理
         * @param {String} type 按钮类型
         */
        handleQuickAccess(type) {
            const currentDate = new Date();
            if (type === '本周') {
                this.week = currentDate;
            } else if (type === '下周') {
                const nextWeekDate = new Date();
                nextWeekDate.setDate(currentDate.getDate() + 7);
                this.week = nextWeekDate;
            }
        },
        /**
         * 获取当前日期的数据
         * @param {Array} rowData 当前行数据
         * @param {String} curDay 日期
         * @param {String} period 上午/下午
         * @param {String} type 类型
         * @returns {Array} 数据
         */
        handleWeekData(rowData, curDay, period, type) {
            const {
                // 评委登记的不可用时间段
                judgesUnavailableTimeList = [],
                // 已经有的会议占用的时间段
                meetingJudgesPartVoList = []
            } = rowData;
            // TODO: 改回来
            // if (
            //     meetingJudgesPartVoList.length > 0 &&
            //     judgesUnavailableTimeList.length === 0
            // ) {
            if (
                meetingJudgesPartVoList.length > 0 &&
                !judgesUnavailableTimeList
            ) {
                const curData = meetingJudgesPartVoList.filter(
                    (i) => i.dateVal === curDay
                );
                if (curData.length === 0) {
                    return type === 'numbers' ? null : [];
                }
                if (type === 'numbers') {
                    return period === '上午'
                        ? curData[0].morningCount
                        : curData[0].afternoonCount;
                }
                return period === '上午'
                    ? curData[0].morningMeetList
                    : curData[0].afternoonMeetList;
            }
            return '';
        },
        isCurrnetUnavailableTimeInPeriod(
            unavailableTimeList,
            curDay,
            period,
            type
        ) {}
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.judge-attendance-container {
    height: calc(100vh - 65px);
}

.query-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    width: calc(100% - 140px);
}

.selector {
    width: 220px;
}

.date-selector {
    height: 28px;
}

@media (max-width: 568px) {
    .selector {
        flex: 1 1 100%;
    }
}
// 修改placeholder颜色
::v-deep .selector .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685);
}
.qucik-access {
    margin-left: 10px;
}
.action-button {
    height: 28px;
    margin-left: 15px;
}

.judge-attendance-table {
    margin-top: 10px;
    border: 1px solid #8c8c8c !important;
}

.pagination {
    padding: 5px 0;
    margin: 10px 0 0 0;
}
// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
